import { get, put } from '@/lib/api'
import { API_ENDPOINTS } from '@/lib/api'
import type {
  UserProfile,
  UpdateProfileRequest,
  ChangePasswordRequest,
} from '@/types/api'

/**
 * 用户服务
 */
export class UserService {
  /**
   * 获取用户资料
   * @returns 用户资料
   */
  static async getProfile() {
    return get<UserProfile>(API_ENDPOINTS.USER.PROFILE)
  }

  /**
   * 更新用户资料
   * @param profile 要更新的用户资料
   * @returns 更新后的用户资料
   */
  static async updateProfile(profile: UpdateProfileRequest) {
    return put<UserProfile>(API_ENDPOINTS.USER.PROFILE, profile)
  }

  /**
   * 修改密码
   * @param passwordData 密码数据
   * @returns 操作结果
   */
  static async changePassword(passwordData: ChangePasswordRequest) {
    return put<void>(API_ENDPOINTS.USER.PASSWORD, passwordData)
  }
}
