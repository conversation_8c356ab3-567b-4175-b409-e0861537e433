import Login from "@/components/features/Authentication/Login/Login"
import NotFound from "@/pages/NotFound"
import ApiTest from "@/pages/ApiTest"
import { BrowserRouter, Routes, Route  } from "react-router-dom"


export function AppRoutes() {
  return (
    <BrowserRouter>
      <Routes >
        <Route path="/login" element={<Login/>} />
        <Route path="/api-test" element={<ApiTest/>} />
        <Route path="*" element={<NotFound />} />
      </Routes>
    </BrowserRouter>
  )
}