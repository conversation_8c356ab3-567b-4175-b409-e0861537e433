# API 接口文档

## 服务器配置

- **服务器地址**: `http://127.0.0.1:8080`
- **请求格式**: JSON
- **响应格式**: JSON

## API 端点列表

### 认证相关

#### 1. 用户登录
- **URL**: `POST /v1/auth/login`
- **描述**: 用户登录获取访问令牌
- **请求体**:
```json
{
  "account": "用户账号",
  "password": "用户密码"
}
```
- **响应**:
```json
{
  "code": 200,
  "message": "登录成功",
  "success": true,
  "data": {
    "token": "访问令牌",
    "refreshToken": "刷新令牌",
    "user": {
      "id": "用户ID",
      "username": "用户名",
      "email": "邮箱",
      "nickname": "昵称"
    }
  }
}
```

#### 2. 刷新令牌
- **URL**: `POST /v1/auth/refresh-token`
- **描述**: 使用刷新令牌获取新的访问令牌
- **请求体**:
```json
{
  "refreshToken": "刷新令牌"
}
```

### 用户相关

#### 3. 获取用户资料
- **URL**: `GET /v1/user/profile`
- **描述**: 获取当前用户的详细资料
- **请求头**: `Authorization: Bearer {token}`

#### 4. 更新用户资料
- **URL**: `PUT /v1/user/profile`
- **描述**: 更新用户资料信息
- **请求头**: `Authorization: Bearer {token}`
- **请求体**:
```json
{
  "nickname": "新昵称",
  "email": "新邮箱",
  "phone": "新手机号",
  "avatar": "头像URL"
}
```

#### 5. 修改密码
- **URL**: `PUT /v1/user/password`
- **描述**: 修改用户密码
- **请求头**: `Authorization: Bearer {token}`
- **请求体**:
```json
{
  "oldPassword": "旧密码",
  "newPassword": "新密码",
  "confirmPassword": "确认新密码"
}
```

### 组织相关

#### 6. 获取组织信息
- **URL**: `GET /v1/org/info`
- **描述**: 获取组织的基本信息
- **请求头**: `Authorization: Bearer {token}`

#### 7. 更新组织信息
- **URL**: `PUT /v1/org/info`
- **描述**: 更新组织信息
- **请求头**: `Authorization: Bearer {token}`
- **请求体**:
```json
{
  "name": "组织名称",
  "description": "组织描述",
  "logo": "组织Logo URL",
  "website": "官网地址",
  "phone": "联系电话",
  "email": "联系邮箱"
}
```

#### 8. 创建地址
- **URL**: `POST /v1/org/addresses`
- **描述**: 为组织创建新地址
- **请求头**: `Authorization: Bearer {token}`
- **请求体**:
```json
{
  "name": "地址名称",
  "province": "省份",
  "city": "城市",
  "district": "区县",
  "detail": "详细地址",
  "postalCode": "邮政编码",
  "isDefault": true
}
```

#### 9. 获取地址列表
- **URL**: `GET /v1/org/addresses`
- **描述**: 获取组织的所有地址
- **请求头**: `Authorization: Bearer {token}`

### 课程相关

#### 10. 获取班级列表
- **URL**: `GET /v1/classes`
- **描述**: 获取所有班级信息
- **请求头**: `Authorization: Bearer {token}`

#### 11. 获取课程信息
- **URL**: `GET /v1/course`
- **描述**: 获取课程详细信息
- **请求头**: `Authorization: Bearer {token}`

## 使用方法

### 1. 在代码中使用

```typescript
import { AuthService, UserService, OrganizationService, CourseService } from '@/lib/services'

// 登录
const loginResponse = await AuthService.login({
  account: '<EMAIL>',
  password: 'password123'
})

// 获取用户资料
const userProfile = await UserService.getProfile()

// 获取组织信息
const orgInfo = await OrganizationService.getInfo()

// 获取班级列表
const classes = await CourseService.getClasses()
```

### 2. 在浏览器控制台测试

在开发环境下，可以在浏览器控制台使用以下命令测试 API：

```javascript
// 测试所有 API
window.testAPI.runAll()

// 测试特定模块
window.testAPI.auth()      // 测试认证 API
window.testAPI.user()      // 测试用户 API
window.testAPI.organization() // 测试组织 API
window.testAPI.course()    // 测试课程 API
```

## 错误处理

所有 API 请求都会返回统一的响应格式：

```json
{
  "code": 200,
  "message": "操作成功",
  "success": true,
  "data": {}
}
```

错误响应示例：

```json
{
  "code": 400,
  "message": "请求参数错误",
  "success": false,
  "data": null
}
```

常见错误码：
- `400`: 请求参数错误
- `401`: 未授权，需要登录
- `403`: 禁止访问
- `404`: 资源不存在
- `500`: 服务器内部错误

## 注意事项

1. 所有需要认证的接口都需要在请求头中携带 `Authorization: Bearer {token}`
2. 令牌过期时会返回 401 错误，需要使用刷新令牌获取新的访问令牌
3. 请求超时时间设置为 10 秒
4. 网络错误会自动重试 3 次
