import { get, put, post } from '@/lib/api'
import { API_ENDPOINTS } from '@/lib/api'
import type {
  OrganizationInfo,
  UpdateOrganizationRequest,
  Address,
  CreateAddressRequest,
} from '@/types/api'

/**
 * 组织服务
 */
export class OrganizationService {
  /**
   * 获取组织信息
   * @returns 组织信息
   */
  static async getInfo() {
    return get<OrganizationInfo>(API_ENDPOINTS.ORG.INFO)
  }

  /**
   * 更新组织信息
   * @param orgData 要更新的组织信息
   * @returns 更新后的组织信息
   */
  static async updateInfo(orgData: UpdateOrganizationRequest) {
    return put<OrganizationInfo>(API_ENDPOINTS.ORG.INFO, orgData)
  }

  /**
   * 获取组织地址列表
   * @returns 地址列表
   */
  static async getAddresses() {
    return get<Address[]>(API_ENDPOINTS.ORG.ADDRESSES)
  }

  /**
   * 创建新地址
   * @param addressData 地址数据
   * @returns 创建的地址
   */
  static async createAddress(addressData: CreateAddressRequest) {
    return post<Address>(API_ENDPOINTS.ORG.ADDRESSES, addressData)
  }
}
