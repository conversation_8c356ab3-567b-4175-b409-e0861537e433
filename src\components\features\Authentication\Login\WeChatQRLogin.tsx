import { useState, useEffect } from 'react'
import { RefreshCw, CheckCircle2, Smartphone } from 'lucide-react'
import { Button } from '@/components/ui/button'
import QRCode from 'qrcode'

// 模拟登录状态
enum LoginStatus {
  WAITING = 'waiting',
  SCANNED = 'scanned',
  SUCCESS = 'success',
  EXPIRED = 'expired',
}

export function WeChatQRLogin() {
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('')
  const [status, setStatus] = useState<LoginStatus>(LoginStatus.WAITING)
  const [countdown, setCountdown] = useState(120) // 2分钟倒计时

  // 生成二维码
  const generateQRCode = async () => {
    try {
      // 模拟微信登录URL
      const loginUrl = `https://login.weixin.qq.com/qrcode/uuid_${Date.now()}`
      const qrCode = await QRCode.toDataURL(loginUrl, {
        width: 200,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF',
        },
      })
      setQrCodeUrl(qrCode)
      setStatus(LoginStatus.WAITING)
      setCountdown(120)
    } catch (error) {
      console.error('生成二维码失败:', error)
    }
  }

  // 模拟扫码状态检查
  const checkScanStatus = () => {
    const timer = setInterval(() => {
      // 随机模拟扫码状态变化
      const random = Math.random()
      if (random < 0.1) { // 10%概率模拟已扫描
        setStatus(LoginStatus.SCANNED)
        setTimeout(() => {
          setStatus(LoginStatus.SUCCESS)
          clearInterval(timer)
        }, 2000)
      }
    }, 3000)

    return timer
  }

  // 倒计时
  useEffect(() => {
    if (status === LoginStatus.WAITING && countdown > 0) {
      const timer = setTimeout(() => {
        setCountdown(countdown - 1)
      }, 1000)
      return () => clearTimeout(timer)
    } else if (countdown === 0) {
      setStatus(LoginStatus.EXPIRED)
    }
  }, [countdown, status])

  // 初始化
  useEffect(() => {
    generateQRCode()
  }, [])

  // 监听扫码状态
  useEffect(() => {
    if (status === LoginStatus.WAITING) {
      const timer = checkScanStatus()
      return () => clearInterval(timer)
    }
  }, [status])

  const getStatusContent = () => {
    switch (status) {
      case LoginStatus.WAITING:
        return (
          <div className="text-center space-y-3">
            <div className="w-48 h-48 mx-auto bg-white rounded-xl p-4 shadow-lg">
              {qrCodeUrl ? (
                <img src={qrCodeUrl} alt="微信登录二维码" className="w-full h-full" />
              ) : (
                <div className="w-full h-full bg-gray-100 rounded-lg flex items-center justify-center">
                  <div className="animate-spin rounded-full border-2 border-gray-300 border-t-blue-500 w-8 h-8"></div>
                </div>
              )}
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-center text-green-600">
                <Smartphone className="w-5 h-5 mr-2" />
                <span className="font-medium">使用微信扫码登录</span>
              </div>
              <p className="text-sm text-gray-500">
                打开微信，扫描上方二维码
              </p>
              <p className="text-xs text-gray-400">
                {Math.floor(countdown / 60)}:{(countdown % 60).toString().padStart(2, '0')} 后过期
              </p>
            </div>
          </div>
        )

      case LoginStatus.SCANNED:
        return (
          <div className="text-center space-y-4">
            <div className="w-48 h-48 mx-auto bg-green-50 rounded-xl flex items-center justify-center">
              <div className="text-center">
                <CheckCircle2 className="w-16 h-16 text-green-500 mx-auto mb-3" />
                <p className="text-green-700 font-medium">扫描成功</p>
              </div>
            </div>
            <div className="space-y-2">
              <p className="text-green-600 font-medium">
                请在手机上确认登录
              </p>
              <p className="text-sm text-gray-500">
                已扫描，请在微信中点击确认
              </p>
            </div>
          </div>
        )

      case LoginStatus.SUCCESS:
        return (
          <div className="text-center space-y-4">
            <div className="w-48 h-48 mx-auto bg-green-50 rounded-xl flex items-center justify-center">
              <div className="text-center">
                <CheckCircle2 className="w-16 h-16 text-green-500 mx-auto mb-3" />
                <p className="text-green-700 font-medium">登录成功</p>
              </div>
            </div>
            <div className="space-y-2">
              <p className="text-green-600 font-medium">
                登录成功，正在跳转...
              </p>
              <div className="flex justify-center">
                <div className="animate-spin rounded-full border-2 border-green-500 border-t-transparent w-5 h-5"></div>
              </div>
            </div>
          </div>
        )

      case LoginStatus.EXPIRED:
        return (
          <div className="text-center space-y-4">
            <div className="w-48 h-48 mx-auto bg-gray-50 rounded-xl flex items-center justify-center">
              <div className="text-center">
                <RefreshCw className="w-16 h-16 text-gray-400 mx-auto mb-3" />
                <p className="text-gray-500 font-medium">二维码已过期</p>
              </div>
            </div>
            <div className="space-y-3">
              <p className="text-gray-500">
                二维码已过期，请刷新后重试
              </p>
              <Button 
                onClick={generateQRCode}
                variant="outline"
                className="w-full"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                刷新二维码
              </Button>
            </div>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className="py-4">
      {getStatusContent()}
    </div>
  )
}