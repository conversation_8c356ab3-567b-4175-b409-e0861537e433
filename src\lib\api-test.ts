/**
 * API 测试工具
 * 用于测试各个 API 端点的连通性
 */

import { AuthService, UserService, OrganizationService, CourseService } from './services'

/**
 * 测试认证相关 API
 */
export async function testAuthAPI() {
  console.group('🔐 测试认证 API')
  
  try {
    // 测试登录
    console.log('测试登录 API...')
    const loginResult = await AuthService.login({
      account: '***********',
      password: '123456'
    })
    console.log('✅ 登录成功:', loginResult)
    
    // 测试刷新令牌
    console.log('测试刷新令牌 API...')
    const refreshResult = await AuthService.refreshToken({
      refreshToken: 'test-refresh-token'
    })
    console.log('✅ 刷新令牌成功:', refreshResult)
    
  } catch (error) {
    console.error('❌ 认证 API 测试失败:', error)
  }
  
  console.groupEnd()
}

/**
 * 测试用户相关 API
 */
export async function testUserAPI() {
  console.group('👤 测试用户 API')
  
  try {
    // 测试获取用户资料
    console.log('测试获取用户资料 API...')
    const profileResult = await UserService.getProfile()
    console.log('✅ 获取用户资料成功:', profileResult)
    
    // 测试更新用户资料
    console.log('测试更新用户资料 API...')
    const updateResult = await UserService.updateProfile({
      nickname: '测试用户',
      email: '<EMAIL>'
    })
    console.log('✅ 更新用户资料成功:', updateResult)
    
    // 测试修改密码
    console.log('测试修改密码 API...')
    const passwordResult = await UserService.changePassword({
      oldPassword: 'oldpassword',
      newPassword: 'newpassword',
      confirmPassword: 'newpassword'
    })
    console.log('✅ 修改密码成功:', passwordResult)
    
  } catch (error) {
    console.error('❌ 用户 API 测试失败:', error)
  }
  
  console.groupEnd()
}

/**
 * 测试组织相关 API
 */
export async function testOrganizationAPI() {
  console.group('🏢 测试组织 API')
  
  try {
    // 测试获取组织信息
    console.log('测试获取组织信息 API...')
    const orgInfoResult = await OrganizationService.getInfo()
    console.log('✅ 获取组织信息成功:', orgInfoResult)
    
    // 测试更新组织信息
    console.log('测试更新组织信息 API...')
    const updateOrgResult = await OrganizationService.updateInfo({
      name: '测试组织',
      description: '这是一个测试组织'
    })
    console.log('✅ 更新组织信息成功:', updateOrgResult)
    
    // 测试获取地址列表
    console.log('测试获取地址列表 API...')
    const addressesResult = await OrganizationService.getAddresses()
    console.log('✅ 获取地址列表成功:', addressesResult)
    
    // 测试创建地址
    console.log('测试创建地址 API...')
    const createAddressResult = await OrganizationService.createAddress({
      name: '测试地址',
      province: '北京市',
      city: '北京市',
      district: '朝阳区',
      detail: '测试街道123号',
      isDefault: true
    })
    console.log('✅ 创建地址成功:', createAddressResult)
    
  } catch (error) {
    console.error('❌ 组织 API 测试失败:', error)
  }
  
  console.groupEnd()
}

/**
 * 测试课程相关 API
 */
export async function testCourseAPI() {
  console.group('📚 测试课程 API')
  
  try {
    // 测试获取班级列表
    console.log('测试获取班级列表 API...')
    const classesResult = await CourseService.getClasses()
    console.log('✅ 获取班级列表成功:', classesResult)
    
    // 测试获取课程信息
    console.log('测试获取课程信息 API...')
    const courseResult = await CourseService.getCourse()
    console.log('✅ 获取课程信息成功:', courseResult)
    
  } catch (error) {
    console.error('❌ 课程 API 测试失败:', error)
  }
  
  console.groupEnd()
}

/**
 * 运行所有 API 测试
 */
export async function runAllAPITests() {
  console.log('🚀 开始运行 API 测试...')
  console.log('服务器地址: http://127.0.0.1:8080')
  console.log('=' .repeat(50))
  
  await testAuthAPI()
  await testUserAPI()
  await testOrganizationAPI()
  await testCourseAPI()
  
  console.log('=' .repeat(50))
  console.log('✨ API 测试完成')
}

// 在开发环境下，可以在控制台调用这些测试函数
if (import.meta.env.DEV) {
  // @ts-ignore
  window.testAPI = {
    runAll: runAllAPITests,
    auth: testAuthAPI,
    user: testUserAPI,
    organization: testOrganizationAPI,
    course: testCourseAPI,
  }
  
  console.log('🔧 开发模式：可以在控制台使用 window.testAPI 来测试 API')
}
