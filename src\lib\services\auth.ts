import { post } from '@/lib/api'
import { API_ENDPOINTS } from '@/lib/api'
import type {
  LoginRequest,
  LoginResponse,
  RefreshTokenRequest,
  RefreshTokenResponse,
} from '@/types/api'

/**
 * 认证服务
 */
export class AuthService {
  /**
   * 用户登录
   * @param credentials 登录凭据
   * @returns 登录响应
   */
  static async login(credentials: LoginRequest) {
    return post<LoginResponse>(API_ENDPOINTS.AUTH.LOGIN, credentials)
  }

  /**
   * 刷新访问令牌
   * @param refreshToken 刷新令牌
   * @returns 新的令牌
   */
  static async refreshToken(refreshToken: RefreshTokenRequest) {
    return post<RefreshTokenResponse>(API_ENDPOINTS.AUTH.REFRESH_TOKEN, refreshToken)
  }

  /**
   * 保存令牌到本地存储
   * @param token 访问令牌
   * @param refreshToken 刷新令牌
   */
  static saveTokens(token: string, refreshToken: string) {
    localStorage.setItem('token', token)
    localStorage.setItem('refreshToken', refreshToken)
  }

  /**
   * 从本地存储获取令牌
   * @returns 令牌对象
   */
  static getTokens() {
    return {
      token: localStorage.getItem('token'),
      refreshToken: localStorage.getItem('refreshToken'),
    }
  }

  /**
   * 清除本地存储的令牌
   */
  static clearTokens() {
    localStorage.removeItem('token')
    localStorage.removeItem('refreshToken')
  }

  /**
   * 检查是否已登录
   * @returns 是否已登录
   */
  static isAuthenticated(): boolean {
    const { token } = this.getTokens()
    return !!token
  }

  /**
   * 登出
   */
  static logout() {
    this.clearTokens()
    // 可以在这里添加其他登出逻辑，比如跳转到登录页
    window.location.href = '/login'
  }
}
