{"name": "pc", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.2.4", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@tailwindcss/vite": "^4.1.11", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "preact": "^10.26.9", "qrcode": "^1.5.4", "react-hook-form": "^7.60.0", "react-router-dom": "^7.6.3", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "zod": "^3.25.76"}, "devDependencies": {"@preact/preset-vite": "^2.10.2", "@types/node": "^24.0.10", "@types/qrcode": "^1.5.5", "@types/react-router-dom": "^5.3.3", "tw-animate-css": "^1.3.5", "typescript": "~5.8.3", "vite": "^7.0.3"}}