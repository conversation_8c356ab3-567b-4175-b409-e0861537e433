import { get } from '@/lib/api'
import { API_ENDPOINTS } from '@/lib/api'
import type {
  ClassInfo,
  CourseInfo,
} from '@/types/api'

/**
 * 课程服务
 */
export class CourseService {
  /**
   * 获取班级列表
   * @returns 班级列表
   */
  static async getClasses() {
    return get<ClassInfo[]>(API_ENDPOINTS.CLASSES)
  }

  /**
   * 获取课程信息
   * @returns 课程信息
   */
  static async getCourse() {
    return get<CourseInfo>(API_ENDPOINTS.COURSE)
  }
}
