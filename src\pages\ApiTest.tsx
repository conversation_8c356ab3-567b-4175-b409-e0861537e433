import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  AuthService, 
  UserService, 
  OrganizationService, 
  CourseService,
  ApiError 
} from '@/lib/services'

export default function ApiTestPage() {
  const [results, setResults] = useState<Record<string, any>>({})
  const [loading, setLoading] = useState<Record<string, boolean>>({})

  const setResult = (key: string, result: any) => {
    setResults(prev => ({ ...prev, [key]: result }))
  }

  const setLoadingState = (key: string, isLoading: boolean) => {
    setLoading(prev => ({ ...prev, [key]: isLoading }))
  }

  const testLogin = async () => {
    const key = 'login'
    setLoadingState(key, true)
    try {
      const result = await AuthService.login({
        account: '<EMAIL>',
        password: 'password123'
      })
      setResult(key, { success: true, data: result })
    } catch (error) {
      setResult(key, { 
        success: false, 
        error: error instanceof ApiError ? error.message : '网络错误' 
      })
    } finally {
      setLoadingState(key, false)
    }
  }

  const testUserProfile = async () => {
    const key = 'userProfile'
    setLoadingState(key, true)
    try {
      const result = await UserService.getProfile()
      setResult(key, { success: true, data: result })
    } catch (error) {
      setResult(key, { 
        success: false, 
        error: error instanceof ApiError ? error.message : '网络错误' 
      })
    } finally {
      setLoadingState(key, false)
    }
  }

  const testOrgInfo = async () => {
    const key = 'orgInfo'
    setLoadingState(key, true)
    try {
      const result = await OrganizationService.getInfo()
      setResult(key, { success: true, data: result })
    } catch (error) {
      setResult(key, { 
        success: false, 
        error: error instanceof ApiError ? error.message : '网络错误' 
      })
    } finally {
      setLoadingState(key, false)
    }
  }

  const testClasses = async () => {
    const key = 'classes'
    setLoadingState(key, true)
    try {
      const result = await CourseService.getClasses()
      setResult(key, { success: true, data: result })
    } catch (error) {
      setResult(key, { 
        success: false, 
        error: error instanceof ApiError ? error.message : '网络错误' 
      })
    } finally {
      setLoadingState(key, false)
    }
  }

  const testCourse = async () => {
    const key = 'course'
    setLoadingState(key, true)
    try {
      const result = await CourseService.getCourse()
      setResult(key, { success: true, data: result })
    } catch (error) {
      setResult(key, { 
        success: false, 
        error: error instanceof ApiError ? error.message : '网络错误' 
      })
    } finally {
      setLoadingState(key, false)
    }
  }

  const renderResult = (key: string) => {
    const result = results[key]
    if (!result) return null

    return (
      <div className="mt-2 p-3 rounded-md text-sm">
        {result.success ? (
          <div className="bg-green-50 text-green-800 border border-green-200">
            <div className="font-medium">✅ 成功</div>
            <pre className="mt-1 text-xs overflow-auto">
              {JSON.stringify(result.data, null, 2)}
            </pre>
          </div>
        ) : (
          <div className="bg-red-50 text-red-800 border border-red-200 p-2 rounded">
            <div className="font-medium">❌ 失败</div>
            <div className="mt-1">{result.error}</div>
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto">
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>API 接口测试</CardTitle>
            <CardDescription>
              测试服务器地址: http://127.0.0.1:8080
            </CardDescription>
          </CardHeader>
        </Card>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* 认证测试 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">🔐 认证接口</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Button 
                  onClick={testLogin}
                  disabled={loading.login}
                  className="w-full"
                >
                  {loading.login ? '测试中...' : 'POST /v1/auth/login'}
                </Button>
                {renderResult('login')}
              </div>
            </CardContent>
          </Card>

          {/* 用户测试 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">👤 用户接口</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Button 
                  onClick={testUserProfile}
                  disabled={loading.userProfile}
                  className="w-full"
                >
                  {loading.userProfile ? '测试中...' : 'GET /v1/user/profile'}
                </Button>
                {renderResult('userProfile')}
              </div>
            </CardContent>
          </Card>

          {/* 组织测试 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">🏢 组织接口</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Button 
                  onClick={testOrgInfo}
                  disabled={loading.orgInfo}
                  className="w-full"
                >
                  {loading.orgInfo ? '测试中...' : 'GET /v1/org/info'}
                </Button>
                {renderResult('orgInfo')}
              </div>
            </CardContent>
          </Card>

          {/* 课程测试 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">📚 课程接口</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Button 
                  onClick={testClasses}
                  disabled={loading.classes}
                  className="w-full mb-2"
                >
                  {loading.classes ? '测试中...' : 'GET /v1/classes'}
                </Button>
                {renderResult('classes')}
              </div>
              
              <div>
                <Button 
                  onClick={testCourse}
                  disabled={loading.course}
                  className="w-full"
                >
                  {loading.course ? '测试中...' : 'GET /v1/course'}
                </Button>
                {renderResult('course')}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
