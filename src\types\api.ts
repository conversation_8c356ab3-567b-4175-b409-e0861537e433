// API 响应基础类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  success: boolean
}

// 分页响应类型
export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 认证相关类型
export interface LoginRequest {
  account: string
  password: string
}

export interface LoginResponse {
  token: string
  refreshToken: string
  user: UserProfile
}

export interface RefreshTokenRequest {
  refreshToken: string
}

export interface RefreshTokenResponse {
  token: string
  refreshToken: string
}

// 用户相关类型
export interface UserProfile {
  id: string
  username: string
  email: string
  phone?: string
  avatar?: string
  nickname?: string
  createdAt: string
  updatedAt: string
}

export interface UpdateProfileRequest {
  nickname?: string
  email?: string
  phone?: string
  avatar?: string
}

export interface ChangePasswordRequest {
  oldPassword: string
  newPassword: string
  confirmPassword: string
}

// 组织相关类型
export interface OrganizationInfo {
  id: string
  name: string
  description?: string
  logo?: string
  website?: string
  phone?: string
  email?: string
  createdAt: string
  updatedAt: string
}

export interface UpdateOrganizationRequest {
  name?: string
  description?: string
  logo?: string
  website?: string
  phone?: string
  email?: string
}

export interface Address {
  id: string
  name: string
  province: string
  city: string
  district: string
  detail: string
  postalCode?: string
  isDefault: boolean
  createdAt: string
  updatedAt: string
}

export interface CreateAddressRequest {
  name: string
  province: string
  city: string
  district: string
  detail: string
  postalCode?: string
  isDefault?: boolean
}

// 课程相关类型
export interface ClassInfo {
  id: string
  name: string
  description?: string
  teacherId: string
  teacherName: string
  capacity: number
  enrolled: number
  startDate: string
  endDate: string
  schedule: string
  status: 'active' | 'inactive' | 'completed'
  createdAt: string
  updatedAt: string
}

export interface CourseInfo {
  id: string
  title: string
  description?: string
  cover?: string
  category: string
  level: 'beginner' | 'intermediate' | 'advanced'
  duration: number // 课程时长（分钟）
  price: number
  originalPrice?: number
  rating: number
  reviewCount: number
  studentCount: number
  status: 'published' | 'draft' | 'archived'
  createdAt: string
  updatedAt: string
}
