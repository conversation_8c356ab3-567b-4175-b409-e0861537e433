import { useNavigate } from 'react-router-dom'
import { Home, ArrowLeft } from 'lucide-react'

export default function NotFound() {
  const navigate = useNavigate()

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-gray-100 flex items-center justify-center p-4">
      <div className="text-center max-w-lg mx-auto">
       
        <div className="mb-12">
          <h1 className="text-[10rem] md:text-[12rem] font-thin text-slate-300 leading-none select-none">
            404
          </h1>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-500 mx-auto -mt-8"></div>
        </div>

        <div className="mb-12 space-y-4">
          <h2 className="text-2xl md:text-3xl font-light text-slate-700 tracking-wide">
            坏了，我好像迷路了。
          </h2>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <button
            onClick={() => navigate('/')}
            className="group flex items-center px-8 py-3 bg-slate-900 text-white font-medium rounded-lg hover:bg-slate-800 transition-all duration-200 min-w-[160px] justify-center"
          >
            <Home className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform" />
            回到首页
          </button>
          
          <button
            onClick={() => navigate(-1)}
            className="group flex items-center px-8 py-3 border border-slate-300 text-slate-700 font-medium rounded-lg hover:border-slate-400 hover:bg-slate-50 transition-all duration-200 min-w-[160px] justify-center"
          >
            <ArrowLeft className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform" />
            返回上一页
          </button>
        </div>
      </div>
    </div>
  )
}