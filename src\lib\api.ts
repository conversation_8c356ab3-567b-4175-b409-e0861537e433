import type { ApiResponse } from '@/types/api'

// API 配置
export const API_CONFIG = {
  BASE_URL: 'http://127.0.0.1:8080',
  TIMEOUT: 10000,
  RETRY_COUNT: 3,
  RETRY_DELAY: 1000,
} as const

// API 端点
export const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    LOGIN: '/v1/auth/login',
    REFRESH_TOKEN: '/v1/auth/refresh-token',
  },
  // 用户相关
  USER: {
    PROFILE: '/v1/user/profile',
    PASSWORD: '/v1/user/password',
  },
  // 组织相关
  ORG: {
    INFO: '/v1/org/info',
    ADDRESSES: '/v1/org/addresses',
  },
  // 课程相关
  CLASSES: '/v1/classes',
  COURSE: '/v1/course',
} as const

// HTTP 状态码
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
} as const

// 错误类型
export class ApiError extends Error {
  constructor(
    public code: number,
    public message: string,
    public data?: any
  ) {
    super(message)
    this.name = 'ApiError'
  }
}

// 请求配置类型
export interface RequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  headers?: Record<string, string>
  body?: any
  timeout?: number
  retryCount?: number
  retryDelay?: number
}

// 获取认证头
export function getAuthHeaders(): Record<string, string> {
  const token = localStorage.getItem('token')
  return token ? { Authorization: `Bearer ${token}` } : {}
}

// 延迟函数
function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

// 基础请求函数
export async function request<T = any>(
  endpoint: string,
  config: RequestConfig = {}
): Promise<ApiResponse<T>> {
  const {
    method = 'GET',
    headers = {},
    body,
    timeout = API_CONFIG.TIMEOUT,
    retryCount = API_CONFIG.RETRY_COUNT,
    retryDelay = API_CONFIG.RETRY_DELAY,
  } = config

  const url = `${API_CONFIG.BASE_URL}${endpoint}`
  
  const requestHeaders: Record<string, string> = {
    'Content-Type': 'application/json',
    ...getAuthHeaders(),
    ...headers,
  }

  const requestConfig: RequestInit = {
    method,
    headers: requestHeaders,
    body: body ? JSON.stringify(body) : undefined,
  }

  let lastError: Error

  for (let attempt = 0; attempt <= retryCount; attempt++) {
    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), timeout)

      const response = await fetch(url, {
        ...requestConfig,
        signal: controller.signal,
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new ApiError(
          response.status,
          errorData.message || `HTTP ${response.status}`,
          errorData
        )
      }

      const data: ApiResponse<T> = await response.json()
      
      if (!data.success) {
        throw new ApiError(data.code, data.message, data.data)
      }

      return data
    } catch (error) {
      lastError = error as Error
      
      // 如果是最后一次尝试，或者是非网络错误，直接抛出
      if (attempt === retryCount || !(error instanceof TypeError)) {
        break
      }

      // 等待后重试
      await delay(retryDelay * Math.pow(2, attempt))
    }
  }

  throw lastError!
}

// GET 请求
export function get<T = any>(endpoint: string, config?: Omit<RequestConfig, 'method' | 'body'>) {
  return request<T>(endpoint, { ...config, method: 'GET' })
}

// POST 请求
export function post<T = any>(endpoint: string, body?: any, config?: Omit<RequestConfig, 'method'>) {
  return request<T>(endpoint, { ...config, method: 'POST', body })
}

// PUT 请求
export function put<T = any>(endpoint: string, body?: any, config?: Omit<RequestConfig, 'method'>) {
  return request<T>(endpoint, { ...config, method: 'PUT', body })
}

// DELETE 请求
export function del<T = any>(endpoint: string, config?: Omit<RequestConfig, 'method' | 'body'>) {
  return request<T>(endpoint, { ...config, method: 'DELETE' })
}

// PATCH 请求
export function patch<T = any>(endpoint: string, body?: any, config?: Omit<RequestConfig, 'method'>) {
  return request<T>(endpoint, { ...config, method: 'PATCH', body })
}
